'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Employee } from '@/src/app/(defaults)/employee/page';

// Sample employee data - in production this would come from API
const employeeData: Employee[] = [
  { id: 1, first_name: '<PERSON>', last_name: '<PERSON>', email: '<EMAIL>', username: 'amartin', specialty: 'HR', contact: '123-456-7890' },
  { id: 2, first_name: '<PERSON>', last_name: '<PERSON>', email: '<EMAIL>', username: 'rlee', specialty: 'IT', contact: '123-456-7891' },
  { id: 3, first_name: '<PERSON>', last_name: '<PERSON>', email: '<EMAIL>', username: 'ngreen', specialty: 'Finance', contact: '123-456-7892' },
  { id: 4, first_name: '<PERSON>', last_name: '<PERSON>', email: '<EMAIL>', username: 'dclark', specialty: 'Marketing', contact: '123-456-7893' },
  { id: 5, first_name: '<PERSON>', last_name: '<PERSON>', email: '<EMAIL>', username: 'charris', specialty: 'Operations', contact: '123-456-7894' },
  { id: 6, first_name: 'Liam', last_name: 'Walker', email: '<EMAIL>', username: 'lwalker', specialty: 'Sales', contact: '123-456-7895' },
  { id: 7, first_name: 'Emma', last_name: 'Robinson', email: '<EMAIL>', username: 'erobinson', specialty: 'Legal', contact: '123-456-7896' },
  { id: 8, first_name: 'Daniel', last_name: 'Lewis', email: '<EMAIL>', username: 'dlewis', specialty: 'Support', contact: '123-456-7897' },
  { id: 9, first_name: 'Grace', last_name: 'Young', email: '<EMAIL>', username: 'gyoung', specialty: 'Design', contact: '123-456-7898' },
  { id: 10, first_name: 'Henry', last_name: 'Scott', email: '<EMAIL>', username: 'hscott', specialty: 'Engineering', contact: '123-456-7899' },
];

interface EmployeeViewProps {
  id: string;
}

const EmployeeView: React.FC<EmployeeViewProps> = ({ id }) => {
  const router = useRouter();
  const [employee, setEmployee] = useState<Employee | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const employeeId = parseInt(id);
    const foundEmployee = employeeData.find(e => e.id === employeeId);

    if (foundEmployee) {
      setEmployee(foundEmployee);
    } else {
      setError('Employee not found');
    }
    setLoading(false);
  }, [id]);

  const handleEdit = () => {
    router.push(`/employee/${id}/edit`);
  };

  const handleBack = () => {
    router.push('/employee');
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-[200px]">
        <div className="text-lg">Loading employee details...</div>
      </div>
    );
  }

  if (error || !employee) {
    return (
      <div className="mx-auto bg-white p-6 rounded shadow-md">
        <div className="text-red-600 text-center">
          <h2 className="text-xl font-bold mb-2">Error</h2>
          <p>{error || 'Employee not found'}</p>
          <button
            type="button"
            onClick={handleBack}
            className="mt-4 bg-gray-500 text-white px-4 py-2 rounded hover:opacity-90"
          >
            Back to Employees List
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="mx-auto bg-white p-6 rounded shadow-md">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold">Employee Details</h2>
        <div className="flex gap-2">
          <button
            type="button"
            onClick={handleEdit}
            className="bg-[#EB6309] text-white px-4 py-2 rounded hover:opacity-90"
          >
            Edit Employee
          </button>
          <button
            type="button"
            onClick={handleBack}
            className="bg-gray-500 text-white px-4 py-2 rounded hover:opacity-90"
          >
            Back to List
          </button>
        </div>
      </div>

      <div className="grid grid-cols-2 gap-6">
        <div>
          <label className="block mb-1 font-semibold text-gray-700">First Name</label>
          <div className="w-full border border-gray-300 px-4 py-2 rounded bg-gray-50">
            {employee.first_name}
          </div>
        </div>

        <div>
          <label className="block mb-1 font-semibold text-gray-700">Last Name</label>
          <div className="w-full border border-gray-300 px-4 py-2 rounded bg-gray-50">
            {employee.last_name}
          </div>
        </div>

        <div>
          <label className="block mb-1 font-semibold text-gray-700">Email</label>
          <div className="w-full border border-gray-300 px-4 py-2 rounded bg-gray-50">
            {employee.email}
          </div>
        </div>

        <div>
          <label className="block mb-1 font-semibold text-gray-700">Username</label>
          <div className="w-full border border-gray-300 px-4 py-2 rounded bg-gray-50">
            {employee.username}
          </div>
        </div>

        <div>
          <label className="block mb-1 font-semibold text-gray-700">Specialty</label>
          <div className="w-full border border-gray-300 px-4 py-2 rounded bg-gray-50">
            {employee.specialty || 'Not specified'}
          </div>
        </div>

        <div>
          <label className="block mb-1 font-semibold text-gray-700">Contact</label>
          <div className="w-full border border-gray-300 px-4 py-2 rounded bg-gray-50">
            {employee.contact || 'Not specified'}
          </div>
        </div>
      </div>
    </div>
  );
};

export default EmployeeView;
