'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';

type Employee = {
  id: number;
  first_name: string;
  last_name: string;
  email: string;
  specialty?: string;
  profile_image?: File | null;
};

const initialEmployee: Employee[] = [
  { id: 1, first_name: '<PERSON>', last_name: '<PERSON>', email: '<EMAIL>' },
  { id: 2, first_name: '<PERSON>', last_name: '<PERSON>', email: '<EMAIL>' },
  { id: 3, first_name: '<PERSON>', last_name: '<PERSON>', email: '<EMAIL>' },
  { id: 4, first_name: '<PERSON>', last_name: '<PERSON>', email: '<EMAIL>' },
  { id: 5, first_name: '<PERSON>', last_name: '<PERSON>', email: '<EMAIL>' },
  { id: 6, first_name: '<PERSON>', last_name: '<PERSON>', email: '<EMAIL>' },
  { id: 7, first_name: '<PERSON>', last_name: '<PERSON>', email: 'emma.<PERSON><PERSON>@company.com' },
  { id: 8, first_name: '<PERSON>', last_name: '<PERSON>', email: '<EMAIL>' },
  { id: 9, first_name: '<PERSON>', last_name: 'Young', email: '<EMAIL>' },
  { id: 10, first_name: 'Henry', last_name: 'Scott', email: '<EMAIL>' },
];

const EditEmployee: React.FC = () => {
  const router = useRouter();
  const [employee, setEmployee] = useState<Employee | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(null);

  useEffect(() => {
    const storedId = sessionStorage.getItem('editEmployeeId');
    if (!storedId) {
      router.push('/employee');
      return;
    }

    const id = parseInt(storedId);
    const foundEmployee = initialEmployee.find((e) => e.id === id);
    if (foundEmployee) {
      setEmployee({ ...foundEmployee });
    } else {
      router.push('/employee');
    }
  }, []);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!employee) return;
    setEmployee({ ...employee, [e.target.name]: e.target.value });
  };

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file || !employee) return;

    setEmployee({ ...employee, profile_image: file });
    setImagePreview(URL.createObjectURL(file));
  };

  const handleSave = () => {
    console.log('Saved Employee:', employee);
    router.push('/employee');
  };

  const handleCancel = () => {
    router.push('/employee');
  };

  if (!employee) return <p>Loading...</p>;

  return (
    <div className="bg-white p-4 rounded shadow-md space-y-4">
      <h2 className="text-2xl font-bold mb-4">Edit Employee</h2>
      <div className="grid grid-cols-2 gap-4">
        <div>
          <label className="font-bold">First Name:</label>
          <input
            value={employee.first_name}
            disabled
            className="border p-2 w-full bg-gray-100"
          />
        </div>
        <div>
          <label className="font-bold">Last Name:</label>
          <input
            name="last_name"
            value={employee.last_name}
            onChange={handleChange}
            className="border p-2 w-full"
          />
        </div>
        <div>
          <label className="font-bold">Email:</label>
          <input
            name="email"
            value={employee.email}
            onChange={handleChange}
            className="border p-2 w-full"
          />
        </div>
        <div>
          <label className="block mb-1 font-semibold">Profile Image</label>
          <input
            type="file"
            accept="image/*"
            onChange={handleImageChange}
            className="w-full border border-gray-300 px-4 py-2 rounded bg-white"
          />
          {imagePreview && (
            <img
              src={imagePreview}
              alt="Preview"
              className="mt-2 w-24 h-24 object-cover rounded border"
            />
          )}
        </div>
      </div>
      <div className="flex gap-2 mt-4">
        <button
          onClick={handleSave}
          className="bg-[#EB6309] text-white px-4 py-2 rounded"
        >
          Save Changes
        </button>
        <button
          onClick={handleCancel}
          className="bg-gray-500 text-white px-4 py-2 rounded"
        >
          Cancel
        </button>
      </div>
    </div>
  );
};

export default EditEmployee;
