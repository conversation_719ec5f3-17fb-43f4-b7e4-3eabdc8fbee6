'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Role } from './Roles';

const permissionsList = ['View', 'Add', 'Edit', 'Delete'];
const modules = ['Retainer', 'Aligner'];

// Sample role data - in production this would come from API
const roleData: Role[] = [
  {
    id: 1,
    name: "Admin",
    permissions: {
      Retainer: ["View", "Add", "Edit", "Delete"],
      Aligner: ["View", "Add", "Edit", "Delete"],
    },
  },
  {
    id: 2,
    name: "Doctor",
    permissions: {
      Retainer: ["View", "Add", "Edit"],
      Aligner: ["View", "Add"],
    },
  },
  {
    id: 3,
    name: "Patient",
    permissions: {
      Retainer: ["View"],
      Aligner: ["View"],
    },
  },
  {
    id: 4,
    name: "Employee",
    permissions: {
      Retainer: ["View", "Add"],
      Aligner: ["View"],
    },
  },
  {
    id: 5,
    name: "User",
    permissions: {
      Retainer: ["View"],
      Aligner: ["View"],
    },
  },
];

type PermissionsState = {
  [key: string]: string[]; // moduleName: [permissions]
};

interface RoleCRUDFormProps {
  mode: 'add' | 'edit';
  id?: string;
}

const RoleCRUDForm: React.FC<RoleCRUDFormProps> = ({ mode, id }) => {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [formData, setFormData] = useState<{
    name: string;
    permissions: PermissionsState;
  }>({
    name: '',
    permissions: {},
  });

  // Load role data for edit mode
  useEffect(() => {
    if (mode === 'edit' && id) {
      setLoading(true);
      const roleId = parseInt(id);
      const foundRole = roleData.find(r => r.id === roleId);
      
      if (foundRole) {
        setFormData({
          name: foundRole.name,
          permissions: foundRole.permissions,
        });
      } else {
        setError('Role not found');
        setTimeout(() => router.push('/roles'), 2000);
      }
      setLoading(false);
    }
  }, [mode, id, router]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleCheckboxChange = (module: string, permission: string) => {
    setFormData((prev) => {
      const currentPermissions = prev.permissions[module] || [];
      const alreadySelected = currentPermissions.includes(permission);
      let updatedModulePermissions = [...currentPermissions];

      if (alreadySelected) {
        // Uncheck permission
        updatedModulePermissions = updatedModulePermissions.filter((p) => p !== permission);

        if (permission === 'View') {
          // Uncheck all if View is unchecked
          updatedModulePermissions = [];
        }
      } else {
        // Add permission
        updatedModulePermissions.push(permission);

        if (permission !== 'View' && !updatedModulePermissions.includes('View')) {
          updatedModulePermissions.push('View'); // Ensure View is selected
        }
      }

      return {
        ...prev,
        permissions: {
          ...prev.permissions,
          [module]: updatedModulePermissions,
        },
      };
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      if (mode === 'add') {
        // In production, this would be an API call
        console.log('Creating new role:', formData);
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Show success message and redirect
        alert('Role created successfully!');
        router.push('/roles');
      } else {
        // In production, this would be an API call
        console.log('Updating role:', { id, ...formData });
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Show success message and redirect
        alert('Role updated successfully!');
        router.push('/roles');
      }
    } catch (err) {
      setError('An error occurred while saving the role');
      console.error('Error saving role:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    router.push('/roles');
  };

  if (loading && mode === 'edit') {
    return (
      <div className="flex justify-center items-center min-h-[200px]">
        <div className="text-lg">Loading role data...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="mx-auto bg-white p-6 rounded shadow-md">
        <div className="text-red-600 text-center">
          <h2 className="text-xl font-bold mb-2">Error</h2>
          <p>{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 mx-auto bg-white shadow rounded">
      <h2 className="text-2xl font-bold mb-4">
        {mode === 'add' ? 'Add New Role' : 'Edit Role'}
      </h2>
      
      <form onSubmit={handleSubmit} className="space-y-6">
        <div>
          <label className="block mb-1 font-semibold">Role Name</label>
          <input
            type="text"
            name="name"
            value={formData.name}
            onChange={handleInputChange}
            className="w-full border border-gray-300 px-4 py-2 rounded focus:outline-none focus:ring-2 focus:ring-orange-500"
            required
            disabled={loading}
          />
        </div>

        <div>
          <h3 className="text-lg font-semibold mb-4">Module Permissions</h3>
          {modules.map((module) => (
            <div key={module} className="mb-6">
              <div className="grid grid-cols-5 gap-5">
                <label className="block mb-2 font-semibold">{module} Module</label>
                {permissionsList.map((permission) => (
                  <label key={permission} className="flex items-center gap-2">
                    <input
                      type="checkbox"
                      value={permission}
                      checked={formData.permissions[module]?.includes(permission) || false}
                      onChange={() => handleCheckboxChange(module, permission)}
                      className="accent-[#EB6309]"
                      disabled={loading}
                    />
                    <span>{permission}</span>
                  </label>
                ))}
              </div>
            </div>
          ))}
        </div>

        <div className="flex gap-4 pt-4">
          <button
            type="submit"
            disabled={loading}
            className="bg-[#EB6309] text-white px-6 py-2 rounded hover:opacity-90 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {loading ? 'Saving...' : (mode === 'add' ? 'Create Role' : 'Update Role')}
          </button>
          
          <button
            type="button"
            onClick={handleCancel}
            disabled={loading}
            className="bg-gray-500 text-white px-6 py-2 rounded hover:opacity-90 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Cancel
          </button>
        </div>
      </form>
    </div>
  );
};

export default RoleCRUDForm;
