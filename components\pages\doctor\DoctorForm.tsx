'use client';
import React, { useState } from 'react';
import <PERSON> from 'next/link';
import { EyeIcon, PencilSquareIcon, TrashIcon } from '@heroicons/react/24/outline';
import Modal from '@/components/Modal';
import { useRouter } from 'next/navigation';
import DataTable from 'react-data-table-component';

type Doctor = {
  id: number;
  first_name: string;
  last_name: string;
  email: string;
  username: string;
  status?: string;
  isToggled?: boolean;
};

const initialDoctors: Doctor[] = [
{ id: 1, first_name: '<PERSON>', last_name: '<PERSON><PERSON>', email: '<EMAIL>', username: 'johndo<PERSON>', status: 'Pending', isToggled: false },
  { id: 2, first_name: '<PERSON>', last_name: '<PERSON>', email: '<EMAIL>', username: 'jane<PERSON>', status: 'Active', isToggled: true },
  { id: 3, first_name: '<PERSON>', last_name: '<PERSON>', email: 'mi<PERSON><PERSON>.joh<PERSON>@example.com', username: 'm<PERSON><PERSON><PERSON>', status: 'Pending', isToggled: false },
  { id: 4, first_name: '<PERSON>', last_name: '<PERSON>', email: '<EMAIL>', username: 'edavis', status: 'Active', isToggled: true },
  { id: 5, first_name: '<PERSON>', last_name: '<PERSON>', email: '<EMAIL>', username: 'wbrown', status: 'Pending', isToggled: false },
  { id: 6, first_name: '<PERSON>', last_name: 'Wilson', email: '<EMAIL>', username: 'owilson', status: 'Active', isToggled: true },
  { id: 7, first_name: 'James', last_name: 'Taylor', email: '<EMAIL>', username: 'jtaylor', status: 'Pending', isToggled: false },
  { id: 8, first_name: 'Sophia', last_name: 'Anderson', email: '<EMAIL>', username: 'sanderson' , status: 'Active', isToggled: true},
  { id: 9, first_name: 'Benjamin', last_name: 'Thomas', email: '<EMAIL>', username: 'bthomas', status: 'Pending', isToggled: false },
  { id: 10, first_name: 'Ava', last_name: 'Moore', email: '<EMAIL>', username: 'amoore' , status: 'Active', isToggled: true},
  { id: 11, first_name: 'Ava', last_name: 'Moore', email: '<EMAIL>', username: 'amoore', status: 'Pending', isToggled: false },
  { id: 12, first_name: 'Ava', last_name: 'Moore', email: '<EMAIL>', username: 'amoore', status: 'Active', isToggled: true },


];

const DoctorForm: React.FC = () => {
  const [search, setSearch] = useState('');

  const [doctors, setDoctors] = useState<Doctor[]>(initialDoctors);

const handleDelete = (id: number) => {
  const confirmDelete = window.confirm("Are you sure you want to delete this doctor?");
  if (confirmDelete) {
    setDoctors(prev => prev.filter(doctor => doctor.id !== id));
  }
};

  const router = useRouter();

  const handleEdit = (id: number) => {
    // Store the ID in sessionStorage
    sessionStorage.setItem('editDoctorId', id.toString());

    // Navigate to the static edit-doctor page
    router.push('/doctor/edit-doctor');
  };
    const handleview = (id: number) => {
    // Store the ID in sessionStorage
    sessionStorage.setItem('editDoctorId', id.toString());

    // Navigate to the static edit-doctor page
    router.push('/doctor/view-doctor');
  };

  const filteredDoctors = doctors.filter(doctor =>
    `${doctor.first_name} ${doctor.last_name} ${doctor.email} ${doctor.username}`
      .toLowerCase()
      .includes(search.toLowerCase())
  );
   const columns = [
    { name: 'First Name', selector: (row: Doctor) => row.first_name, sortable: true },
    { name: 'Last Name', selector: (row: Doctor) => row.last_name, sortable: true },
    { name: 'Email', selector: (row: Doctor) => row.email, sortable: true },
    { name: 'Username', selector: (row: Doctor) => row.username, sortable: true },
    // Status Dropdown Column
{
  name: 'Status',
  cell: (row: Doctor) => (
    <select
      className="border border-gray-300 px-2 py-1 rounded"
      onChange={(e) => {
        const newStatus = e.target.value;
        setDoctors((prev) =>
          prev.map((doc) =>
            doc.id === row.id ? { ...doc, status: newStatus } : doc
          )
        );
      }}
      value={row.status || 'Active'}
    >
      <option value="Active">Active</option>
      <option value="Inactive">Inactive</option>
      <option value="Pending">Pending</option>
    </select>
  ),
},

// Toggle Switch Column
{
  name: 'Toggle',
  cell: (row: Doctor) => (
    <label className="relative inline-flex items-center cursor-pointer">
      <input
        type="checkbox"
        className="sr-only peer"
        checked={row.isToggled || false}
        onChange={() => {
          setDoctors((prev) =>
            prev.map((doc) =>
              doc.id === row.id
                ? { ...doc, isToggled: !doc.isToggled }
                : doc
            )
          );
        }}
      />
      <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-orange-500 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-orange-600"></div>
    </label>
  ),
},

    {
      name: 'Actions',
      cell: (row: Doctor) => (
        <div className="flex gap-3 items-center">
          <button onClick={() => handleview(row.id)} title="View">
            <EyeIcon className="h-5 w-5 text-blue-500 hover:text-blue-700" />
          </button>
          <button onClick={() => handleEdit(row.id)} title="Edit">
            <PencilSquareIcon className="h-5 w-5 text-green-500 hover:text-green-700" />
          </button>
          <button onClick={() => handleDelete(row.id)} title="Delete">
            <TrashIcon className="h-5 w-5 text-red-500 hover:text-red-700" />
          </button>
        </div>
      ),
    },
  ];

    return (
    <div className="p-4 bg-white rounded shadow">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-2xl font-bold">Doctors List</h2>
        <button
          onClick={() => router.push('/doctor/add-doctor')}
          className="bg-orange-600 text-white px-4 py-2 rounded"
        >
          Add Doctor
        </button>
      </div>

      <input
        type="text"
        placeholder="Search by name, email, or username..."
        value={search}
        onChange={e => setSearch(e.target.value)}
        className="float-right w-full sm:w-1/3 mb-4 p-2 border border-gray-300 rounded"
      />

      <DataTable
        columns={columns}
        data={filteredDoctors}
        pagination
        highlightOnHover
        responsive
        striped
        noHeader
      />
    </div>
  );
};

export default DoctorForm;
