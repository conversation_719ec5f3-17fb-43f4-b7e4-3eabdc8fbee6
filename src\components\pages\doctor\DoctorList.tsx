'use client';
import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { EyeIcon, PencilSquareIcon, TrashIcon } from '@heroicons/react/24/outline';
import { useRouter } from 'next/navigation';
import DataTable from 'react-data-table-component';
import { Doctor } from './DoctorModule';
import { doctorService } from '@/src/api/doctor';

const DoctorList: React.FC = () => {
  const [search, setSearch] = useState('');
  const [doctors, setDoctors] = useState<Doctor[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();

  // Fetch doctors from API
  useEffect(() => {
    const fetchDoctors = async () => {
      try {
        setLoading(true);
        setError(null);
        const response = await doctorService.getDoctors();

        if (response.success && response.data) {
          setDoctors(response.data);
        } else {
          setError(response.message || 'Failed to fetch doctors');
        }
      } catch (err) {
        console.error('Error fetching doctors:', err);
        setError('An error occurred while fetching doctors');
      } finally {
        setLoading(false);
      }
    };

    fetchDoctors();
  }, []);

  const handleDelete = async (id: number) => {
    if (window.confirm("Are you sure you want to delete this doctor?")) {
      try {
        const response = await doctorService.deleteDoctor(id.toString());
        if (response.success) {
          setDoctors(prev => prev.filter(d => d.id !== id));
        } else {
          alert(response.message || 'Failed to delete doctor');
        }
      } catch (err) {
        console.error('Error deleting doctor:', err);
        alert('An error occurred while deleting the doctor');
      }
    }
  };
  const handleView = (id: number) => router.push(`/doctor/${id}?mode=view`);
  const handleEdit = (id: number) => router.push(`/doctor/${id}?mode=edit`);

  const filtered = doctors.filter(d =>
    `${d.first_name} ${d.last_name} ${d.email} ${d.username}`
      .toLowerCase()
      .includes(search.toLowerCase())
  );

  const columns = [
    { name: 'First Name', selector: (r: Doctor) => r.first_name, sortable: true },
    { name: 'Last Name',  selector: (r: Doctor) => r.last_name,  sortable: true },
    { name: 'Email',      selector: (r: Doctor) => r.email,      sortable: true },
    { name: 'Username',   selector: (r: Doctor) => r.username,   sortable: true },
    // { name: 'Specialty',  selector: (r: Doctor) => r.specialty || 'Not specified', sortable: true },
    {
      name: 'Actions',
      cell: (row: Doctor) => (
        <div className="flex gap-3 items-center">
          <button type="button" onClick={() => handleView(row.id)} title="View">
            <EyeIcon className="h-5 w-5 text-blue-500 hover:text-blue-700" />
          </button>
          <button type="button" onClick={() => handleEdit(row.id)} title="Edit">
            <PencilSquareIcon className="h-5 w-5 text-green-500 hover:text-green-700" />
          </button>
          <button type="button" onClick={() => handleDelete(row.id)} title="Delete">
            <TrashIcon className="h-5 w-5 text-red-500 hover:text-red-700" />
          </button>
        </div>
      ),
    },
  ];

  // Show loading state
  if (loading) {
    return (
      <div className="p-4 bg-white rounded shadow">
        <div className="flex items-center justify-center h-64">
          <div className="text-lg">Loading doctors...</div>
        </div>
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className="p-4 bg-white rounded shadow">
        <div className="flex items-center justify-center h-64">
          <div className="text-red-500 text-lg">{error}</div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-4 bg-white rounded shadow">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-2xl font-bold">Doctors List</h2>
        <button
          type="button"
          onClick={() => router.push('/doctor/add')}
          className="bg-[#EB6309] text-white px-4 py-2 rounded hover:opacity-90"
        >
          Add Doctor
        </button>
      </div>

      <input
        type="text"
        placeholder="Search by name, email, or username..."
        value={search}
        onChange={e => setSearch(e.target.value)}
        className="float-right w-full sm:w-1/3 mb-4 p-2 border border-gray-300 rounded"
      />

      <DataTable
        columns={columns}
        data={filtered}
        pagination
        highlightOnHover
        responsive
        striped
        noHeader
      />
    </div>
  );
};

export default DoctorList;
