'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';


const AddDoctorPage = () => {
  const router = useRouter();
  const [formData, setFormData] = useState({
    first_name: '',
    last_name: '',
    email: '',
    username: '',
    specialty: '',
    contact: '',
     profile_image: null as File | null,
  });

  const [imagePreview, setImagePreview] = useState<string | null>(null);

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setFormData({ ...formData, profile_image: file });
      setImagePreview(URL.createObjectURL(file));
    }
  };


  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Simulate saving doctor (e.g., send to API or save to state)
    console.log('New Doctor:', formData);

    // Redirect to doctor list or show success message
    router.push('/doctor'); // adjust this path if needed
  };

  return (
    <div className=" mx-auto bg-white p-6 rounded shadow-md">
      <h2 className="text-2xl font-bold mb-6">Add Doctor</h2>
      <form onSubmit={handleSubmit} className=" space-y-4">
        <div className=" grid grid-cols-2 gap-4">
          <div>
          <label className="block mb-1 font-semibold">First Name</label>
          <input
            type="text"
            value={formData.first_name}
            onChange={(e) =>
              setFormData({ ...formData, first_name: e.target.value })
            }
            className="w-full border border-gray-300 px-4 py-2 rounded"
            required
          />
        </div>
        <div>
          <label className="block mb-1 font-semibold">Last Name</label>
          <input
            type="text"
            value={formData.last_name}
            onChange={(e) =>
              setFormData({ ...formData, last_name: e.target.value })
            }
            className="w-full border border-gray-300 px-4 py-2 rounded"
            required
          />
        </div>

        <div>
          <label className="block mb-1 font-semibold">Email</label>
          <input
            type="email"
            value={formData.email}
            onChange={(e) =>
              setFormData({ ...formData, email: e.target.value })
            }
            className="w-full border border-gray-300 px-4 py-2 rounded"
            required
          />
        </div>
        <div>
          <label className="block mb-1 font-semibold">Specialty</label>
          <input
            type="text"
            value={formData.specialty}
            onChange={(e) =>
              setFormData({ ...formData, specialty: e.target.value })
            }
            className="w-full border border-gray-300 px-4 py-2 rounded"
          />
        </div>
        <div>
            <label className="block mb-1 font-semibold">Profile Image</label>
            <input
              type="file"
              accept="image/*"
              onChange={handleImageChange}
              className="w-full border border-gray-300 px-4 py-2 rounded bg-white"
            />
            {imagePreview && (
              <img
                src={imagePreview}
                alt="Preview"
                className="mt-2 w-24 h-24 object-cover rounded border"
              />
            )}
          </div>
        </div>

        <button
          type="submit"
          className="bg-[#EB6309] text-white px-4 py-2 rounded hover:opacity-90"
        >
          Save Doctor
        </button>
      </form>
    </div>
  );
};

export default AddDoctorPage;
