'use client';
import React, { useState } from 'react';
import { EyeIcon, PencilSquareIcon, TrashIcon } from '@heroicons/react/24/outline';
import { useRouter } from 'next/navigation';
import DataTable from 'react-data-table-component';

type Employee = {
  id: number;
  first_name: string;
  last_name: string;
  email: string;
  username: string;
   status?: 'Active' | 'Inactive' | 'Pending';
  isToggled?: boolean;
};

const initialEmployee: Employee[] = [
  { id: 1, first_name: '<PERSON>', last_name: '<PERSON>', email: '<EMAIL>', username: 'amartin', status: 'Active', isToggled: true },
  { id: 2, first_name: '<PERSON>', last_name: '<PERSON>', email: '<EMAIL>', username: 'rlee', status: 'Pending', isToggled: false },
  { id: 3, first_name: '<PERSON>', last_name: '<PERSON>', email: '<EMAIL>', username: 'ng<PERSON>' , status: '<PERSON>', isToggled: true },
  { id: 4, first_name: '<PERSON>', last_name: '<PERSON>', email: '<EMAIL>', username: 'dclar<PERSON>', status: 'Pending', isToggled: false },
  { id: 5, first_name: '<PERSON>', last_name: 'Harris', email: '<EMAIL>', username: 'charris' , status: 'Active', isToggled: true },
  { id: 6, first_name: 'Liam', last_name: 'Walker', email: '<EMAIL>', username: 'lwalker', status: 'Active', isToggled: true },
  { id: 7, first_name: 'Emma', last_name: 'Robinson', email: '<EMAIL>', username: 'erobinson', status: 'Pending', isToggled: false },
  { id: 8, first_name: 'Daniel', last_name: 'Lewis', email: '<EMAIL>', username: 'dlewis', status: 'Active', isToggled: true },
  { id: 9, first_name: 'Grace', last_name: 'Young', email: '<EMAIL>', username: 'gyoung', status: 'Pending', isToggled: false },
  { id: 10, first_name: 'Henry', last_name: 'Scott', email: '<EMAIL>', username: 'hscott', status: 'Active', isToggled: true },
  { id: 11, first_name: 'Henry', last_name: 'Scott', email: '<EMAIL>', username: 'hscott', status: 'Pending', isToggled: false },
];

const EmployeeForm: React.FC = () => {
  const [employees, setEmployees] = useState<Employee[]>(initialEmployee);
  const [search, setSearch] = useState('');
  const router = useRouter();

  const handleDelete = (id: number) => {
    const confirmDelete = window.confirm("Are you sure you want to delete this employee?");
  if (confirmDelete) {
       setEmployees(prev => prev.filter(employee => employee.id !== id));

  }
  };

 const handleEdit = (id: number) => {
  sessionStorage.setItem('editEmployeeId', id.toString());
  router.push('/employee/edit-employee');
};

  const handleView = (id: number) => {
    sessionStorage.setItem('editEmployeeId', id.toString());
    router.push('/employee/view-employee');
  };

  const filteredEmployees = employees.filter(employee =>
    `${employee.first_name} ${employee.last_name} ${employee.email} ${employee.username}`
      .toLowerCase()
      .includes(search.toLowerCase())
  );

const columns = [
  { name: 'First Name', selector: (row: Employee) => row.first_name, sortable: true },
  { name: 'Last Name', selector: (row: Employee) => row.last_name, sortable: true },
  { name: 'Email', selector: (row: Employee) => row.email, sortable: true },
  { name: 'Username', selector: (row: Employee) => row.username, sortable: true },

  // Status Dropdown Column
  {
    name: 'Status',
    cell: (row: Employee) => (
      <select
        value={row.status || 'Pending'}
        onChange={(e) => {
          const updatedStatus = e.target.value as Employee['status'];
          setEmployees((prev) =>
            prev.map((emp) =>
              emp.id === row.id ? { ...emp, status: updatedStatus } : emp
            )
          );
        }}
        className="border border-gray-300 px-2 py-1 rounded text-sm"
      >
        <option>Active</option>
        <option>Inactive</option>
        <option>Pending</option>
      </select>
    ),
    sortable: true,
  },

  // Toggle Switch Column
  {
    name: 'Toggle',
    cell: (row: Employee) => (
      <label className="relative inline-flex items-center cursor-pointer">
      <input
        type="checkbox"
        className="sr-only peer"
        checked={row.isToggled || false}
        onChange={() => {
          setEmployees((prev) =>
            prev.map((emp) =>
              emp.id === row.id ? { ...emp, isToggled: !emp.isToggled } : emp
            )
          );
        }}
      />
      <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-orange-500 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-orange-600"></div>
    </label>
    ),
  },

  // Actions Column
  {
    name: 'Actions',
    cell: (row: Employee) => (
      <div className="flex gap-2">
        <button onClick={() => handleView(row.id)} title="View">
          <EyeIcon className="h-5 w-5 text-blue-500 hover:text-blue-700" />
        </button>
        <button onClick={() => handleEdit(row.id)} title="Edit">
          <PencilSquareIcon className="h-5 w-5 text-green-500 hover:text-green-700" />
        </button>
        <button onClick={() => handleDelete(row.id)} title="Delete">
          <TrashIcon className="h-5 w-5 text-red-500 hover:text-red-700" />
        </button>
      </div>
    ),
  },
];

  return (
    <div className="p-4 bg-white rounded shadow">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-2xl font-bold">Employee List</h2>
        <button
          onClick={() => router.push('/employee/add-employee')}
          className="bg-orange-600 text-white px-4 py-2 rounded"
        >
          Add Employee
        </button>
      </div>

      <input
        type="text"
        placeholder="Search by name, email, or username..."
        value={search}
        onChange={e => setSearch(e.target.value)}
        className="float-right w-full sm:w-1/3 mb-4 p-2 border border-gray-300 rounded"
      />

      <DataTable
        columns={columns}
        data={filteredEmployees}
        pagination
        highlightOnHover
        responsive
        striped
        noHeader
      />
    </div>
  );
};

export default EmployeeForm;
