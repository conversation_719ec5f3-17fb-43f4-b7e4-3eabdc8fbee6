'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Employee } from '@/src/app/(defaults)/employee/page';

// Sample employee data - in production this would come from API
const employeeData: Employee[] = [
  { id: 1, first_name: '<PERSON>', last_name: '<PERSON>', email: '<EMAIL>', username: 'amartin', specialty: 'HR', contact: '123-456-7890' },
  { id: 2, first_name: '<PERSON>', last_name: '<PERSON>', email: '<EMAIL>', username: 'rlee', specialty: 'IT', contact: '123-456-7891' },
  { id: 3, first_name: '<PERSON>', last_name: '<PERSON>', email: '<EMAIL>', username: 'ngreen', specialty: 'Finance', contact: '123-456-7892' },
  { id: 4, first_name: '<PERSON>', last_name: '<PERSON>', email: '<EMAIL>', username: 'dclark', specialty: 'Marketing', contact: '123-456-7893' },
  { id: 5, first_name: '<PERSON>', last_name: '<PERSON>', email: '<EMAIL>', username: 'charris', specialty: 'Operations', contact: '123-456-7894' },
  { id: 6, first_name: 'Liam', last_name: 'Walker', email: '<EMAIL>', username: 'lwalker', specialty: 'Sales', contact: '123-456-7895' },
  { id: 7, first_name: 'Emma', last_name: 'Robinson', email: '<EMAIL>', username: 'erobinson', specialty: 'Legal', contact: '123-456-7896' },
  { id: 8, first_name: 'Daniel', last_name: 'Lewis', email: '<EMAIL>', username: 'dlewis', specialty: 'Support', contact: '123-456-7897' },
  { id: 9, first_name: 'Grace', last_name: 'Young', email: '<EMAIL>', username: 'gyoung', specialty: 'Design', contact: '123-456-7898' },
  { id: 10, first_name: 'Henry', last_name: 'Scott', email: '<EMAIL>', username: 'hscott', specialty: 'Engineering', contact: '123-456-7899' },
];

interface EmployeeCRUDFormProps {
  mode: 'add' | 'edit';
  id?: string;
}

const EmployeeCRUDForm: React.FC<EmployeeCRUDFormProps> = ({ mode, id }) => {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [formData, setFormData] = useState<Omit<Employee, 'id'>>({
    first_name: '',
    last_name: '',
    email: '',
    username: '',
    specialty: '',
    contact: '',
  });

  // Load employee data for edit mode
  useEffect(() => {
    if (mode === 'edit' && id) {
      setLoading(true);
      const employeeId = parseInt(id);
      const foundEmployee = employeeData.find(e => e.id === employeeId);

      if (foundEmployee) {
        setFormData({
          first_name: foundEmployee.first_name,
          last_name: foundEmployee.last_name,
          email: foundEmployee.email,
          username: foundEmployee.username,
          specialty: foundEmployee.specialty || '',
          contact: foundEmployee.contact || '',
        });
      } else {
        setError('Employee not found');
        setTimeout(() => router.push('/employee'), 2000);
      }
      setLoading(false);
    }
  }, [mode, id, router]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      if (mode === 'add') {
        // In production, this would be an API call
        console.log('Creating new employee:', formData);
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Show success message and redirect
        alert('Employee created successfully!');
        router.push('/employee');
      } else {
        // In production, this would be an API call
        console.log('Updating employee:', { id, ...formData });
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Show success message and redirect
        alert('Employee updated successfully!');
        router.push('/employee');
      }
    } catch (err) {
      setError('An error occurred while saving the employee');
      console.error('Error saving employee:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    router.push('/employee');
  };

  if (loading && mode === 'edit') {
    return (
      <div className="flex justify-center items-center min-h-[200px]">
        <div className="text-lg">Loading employee data...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="mx-auto bg-white p-6 rounded shadow-md">
        <div className="text-red-600 text-center">
          <h2 className="text-xl font-bold mb-2">Error</h2>
          <p>{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="mx-auto bg-white p-6 rounded shadow-md">
      <h2 className="text-2xl font-bold mb-6">
        {mode === 'add' ? 'Add Employee' : 'Edit Employee'}
      </h2>

      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="block mb-1 font-semibold">First Name</label>
            <input
              type="text"
              name="first_name"
              value={formData.first_name}
              onChange={handleInputChange}
              className="w-full border border-gray-300 px-4 py-2 rounded focus:outline-none focus:ring-2 focus:ring-orange-500"
              required
              disabled={loading || (mode === 'edit')} // Disable first name in edit mode like the original
            />
          </div>

          <div>
            <label className="block mb-1 font-semibold">Last Name</label>
            <input
              type="text"
              name="last_name"
              value={formData.last_name}
              onChange={handleInputChange}
              className="w-full border border-gray-300 px-4 py-2 rounded focus:outline-none focus:ring-2 focus:ring-orange-500"
              required
              disabled={loading}
            />
          </div>

          <div>
            <label className="block mb-1 font-semibold">Email</label>
            <input
              type="email"
              name="email"
              value={formData.email}
              onChange={handleInputChange}
              className="w-full border border-gray-300 px-4 py-2 rounded focus:outline-none focus:ring-2 focus:ring-orange-500"
              required
              disabled={loading}
            />
          </div>

          <div>
            <label className="block mb-1 font-semibold">Username</label>
            <input
              type="text"
              name="username"
              value={formData.username}
              onChange={handleInputChange}
              className="w-full border border-gray-300 px-4 py-2 rounded focus:outline-none focus:ring-2 focus:ring-orange-500"
              required
              disabled={loading}
            />
          </div>

          <div>
            <label className="block mb-1 font-semibold">Specialty</label>
            <input
              type="text"
              name="specialty"
              value={formData.specialty}
              onChange={handleInputChange}
              className="w-full border border-gray-300 px-4 py-2 rounded focus:outline-none focus:ring-2 focus:ring-orange-500"
              disabled={loading}
            />
          </div>

          <div>
            <label className="block mb-1 font-semibold">Contact</label>
            <input
              type="text"
              name="contact"
              value={formData.contact}
              onChange={handleInputChange}
              className="w-full border border-gray-300 px-4 py-2 rounded focus:outline-none focus:ring-2 focus:ring-orange-500"
              disabled={loading}
            />
          </div>
        </div>

        <div className="flex gap-4 pt-4">
          <button
            type="submit"
            disabled={loading}
            className="bg-[#EB6309] text-white px-6 py-2 rounded hover:opacity-90 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {loading ? 'Saving...' : (mode === 'add' ? 'Save Employee' : 'Save Changes')}
          </button>

          <button
            type="button"
            onClick={handleCancel}
            disabled={loading}
            className="bg-gray-500 text-white px-6 py-2 rounded hover:opacity-90 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Cancel
          </button>
        </div>
      </form>
    </div>
  );
};

export default EmployeeCRUDForm;
