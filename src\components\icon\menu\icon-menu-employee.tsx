import { FC } from "react";

interface IconMenuEmployeeProps {
  className?: string;
}

const IconMenuEmployee: FC<IconMenuEmployeeProps> = ({ className }) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      fill="none"
      stroke="currentColor"
      stroke-width="1.8"
      stroke-linecap="round"
      stroke-linejoin="round"
      viewBox="0 0 24 24"
    >
      <circle cx="9" cy="7" r="3" />
      <circle cx="17" cy="7" r="3" />
      <path d="M2 21v-2a4 4 0 014-4h3a4 4 0 014 4v2" />
      <path d="M13 21v-2a4 4 0 014-4h3a4 4 0 014 4v2" />
    </svg>
  );
};

export default IconMenuEmployee;
