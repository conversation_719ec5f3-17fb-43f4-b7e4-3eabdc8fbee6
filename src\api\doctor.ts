import { AxiosResponse } from 'axios';
import { DOCTOR_ENDPOINTS } from '@/src/utils/apiRoutes';
import apiClient from '@/src/utils/apiClient';

// Types for doctor API
export interface Doctor {
  id: number;
  first_name: string;
  last_name: string;
  email: string;
  username: string;
  specialty?: string;
  contact?: string;
  phone?: string;
  address?: string;
  created_at?: string;
  updated_at?: string;
}

export interface DoctorsResponse {
  success: boolean;
  message: string;
  data?: Doctor[];
  error?: string;
}

export interface DoctorResponse {
  success: boolean;
  message: string;
  data?: Doctor;
  error?: string;
}

export interface CreateDoctorRequest {
  first_name: string;
  last_name: string;
  email: string;
  username: string;
  specialty?: string;
  contact?: string;
  phone?: string;
  address?: string;
}

export interface UpdateDoctorRequest extends Partial<CreateDoctorRequest> {
  id: number;
}

// Doctor API service
export const doctorService = {
  // Get all doctors
  async getDoctors(): Promise<DoctorsResponse> {
    try {
      const response: AxiosResponse<DoctorsResponse> = await apiClient.get(
        DOCTOR_ENDPOINTS.GET_DOCTORS
      );

      return response.data;
    } catch (error: any) {
      console.error('Get doctors error:', error);

      // Handle different error scenarios
      if (error.response?.data) {
        return {
          success: false,
          message: error.response.data.message || 'Failed to fetch doctors',
          error: error.response.data.error || 'API error',
        };
      }

      return {
        success: false,
        message: 'Network error. Please check your connection.',
        error: 'Network error',
      };
    }
  },

  // Get single doctor by ID
  async getDoctor(id: string): Promise<DoctorResponse> {
    try {
      const response: AxiosResponse<DoctorResponse> = await apiClient.get(
        DOCTOR_ENDPOINTS.GET_DOCTOR(id)
      );

      return response.data;
    } catch (error: any) {
      console.error('Get doctor error:', error);

      if (error.response?.data) {
        return {
          success: false,
          message: error.response.data.message || 'Failed to fetch doctor',
          error: error.response.data.error || 'API error',
        };
      }

      return {
        success: false,
        message: 'Network error. Please check your connection.',
        error: 'Network error',
      };
    }
  },

  // Create new doctor
  async createDoctor(doctorData: CreateDoctorRequest): Promise<DoctorResponse> {
    try {
      const response: AxiosResponse<DoctorResponse> = await apiClient.post(
        DOCTOR_ENDPOINTS.CREATE_DOCTOR,
        doctorData
      );

      return response.data;
    } catch (error: any) {
      console.error('Create doctor error:', error);

      if (error.response?.data) {
        return {
          success: false,
          message: error.response.data.message || 'Failed to create doctor',
          error: error.response.data.error || 'API error',
        };
      }

      return {
        success: false,
        message: 'Network error. Please check your connection.',
        error: 'Network error',
      };
    }
  },

  // Update existing doctor
  async updateDoctor(id: string, doctorData: Partial<CreateDoctorRequest>): Promise<DoctorResponse> {
    try {
      const response: AxiosResponse<DoctorResponse> = await apiClient.put(
        DOCTOR_ENDPOINTS.UPDATE_DOCTOR(id),
        doctorData
      );

      return response.data;
    } catch (error: any) {
      console.error('Update doctor error:', error);

      if (error.response?.data) {
        return {
          success: false,
          message: error.response.data.message || 'Failed to update doctor',
          error: error.response.data.error || 'API error',
        };
      }

      return {
        success: false,
        message: 'Network error. Please check your connection.',
        error: 'Network error',
      };
    }
  },

  // Delete doctor
  async deleteDoctor(id: string): Promise<DoctorResponse> {
    try {
      const response: AxiosResponse<DoctorResponse> = await apiClient.delete(
        DOCTOR_ENDPOINTS.DELETE_DOCTOR(id)
      );

      return response.data;
    } catch (error: any) {
      console.error('Delete doctor error:', error);

      if (error.response?.data) {
        return {
          success: false,
          message: error.response.data.message || 'Failed to delete doctor',
          error: error.response.data.error || 'API error',
        };
      }

      return {
        success: false,
        message: 'Network error. Please check your connection.',
        error: 'Network error',
      };
    }
  },
};

export default doctorService;
