/**
 * API Endpoints for the application
 */

// Base API URL
export const API_BASE_URL =
  process.env.NEXT_PUBLIC_API_URL || "http://localhost:5008/api/v1";

// Auth endpoints
export const AUTH_ENDPOINTS = {
  LOGIN: "/auth/login-admin",
  LOGOUT: "/auth/logout",
  FORGOT_PASSWORD: "/auth/forgot-password",
  RESET_PASSWORD: "/auth/reset-password",
};

// User endpoints
export const USER_ENDPOINTS = {
  GET_PROFILE: "/auth/admin-profile",
  UPDATE_PROFILE: "/auth/admin-profile", // Changed to match the backend function name
  GET_USERS: "/admin/users",
  GET_USER: (id: string) => `/users/${id}`,
  CREATE_USER: "/users",
  UPDATE_USER: (id: string) => `/users/${id}`,
  DELETE_USER: (id: string) => `/users/${id}`,
};

// Dashboard endpoints
export const DASHBOARD_ENDPOINTS = {
  GET_STATS: "/admin/dashboard/statistics",
};

// Settings endpoints
export const SETTINGS_ENDPOINTS = {
  GET_SETTINGS: "/admin/settings",
  UPDATE_SETTINGS: "/admin/settings",
};

export const DRIVERS_ENDPOINTS = {
  ADD_DRIVER: "/admin/drivers",
  UPDATE_DRIVER: "/admin/drivers",
  VIEW_DRIVER: "/admin/drivers",
  ALL_DRIVERS: "/admin/drivers",
};

export const SHIPMENTS_ENDPOINTS = {
  GET_SHIPMENTS: "/admin/shipments", // GET    all shipments
  ADD_SHIPMENT: "/admin/shipments", // POST   new shipment
  VIEW_SHIPMENT: "/admin/shipments", // GET    /:id
  UPDATE_SHIPMENT: "/admin/shipments", // PATCH  /:id
  DELETE_SHIPMENT: "/admin/shipments", // DELETE /:id
  CHANGE_DRIVER: "/admin/shipments/change/tagged-driver", // PATCH /:container_id
  REMOVE_DRIVER: "/admin/shipments/remove/tagged-driver", // PATCH /:container_id
  UPDATE_CONTAINER_STATUS: "/admin/shipments/toggle-status", // PATCH /:container_id
  VERIFY_DELIVERY_OTP: "/admin/shipments/container/verify-otp", // POST /:container_id
  CONFIRM_DELIVERY: "/admin/shipments/container/confirm-delivery", // POST /:container_id
};

// Task details endpoints
export const TASK_ENDPOINTS = {
  GET_TASKS: "/admin/tasks", // GET    all tasks
  UPDATE_TASK: "/admin/tasks", // PATCH  /:id
  GET_DELIVERY_PROOF: "/admin/tasks/delivery-proof", // GET /:container_id
  SUBMIT_DELIVERY_PROOF: "/admin/tasks/delivery-proof", // POST /:container_id
};

// Notification endpoints
export const NOTIFICATION_ENDPOINTS = {
  GET_NOTIFICATIONS: "/admin/notifications", // GET all notifications
  MARK_AS_READ: "/admin/notifications", // PATCH /:id/read
  MARK_ALL_AS_READ: "/admin/notifications/read-all", // PATCH
};

// Doctor endpoints
export const DOCTOR_ENDPOINTS = {
  GET_DOCTORS: "/user/doctors", // GET all doctors
  GET_DOCTOR: (id: string) => `/user/doctors/${id}`, // GET specific doctor
  CREATE_DOCTOR: "/user/doctors", // POST new doctor
  UPDATE_DOCTOR: (id: string) => `/user/doctors/${id}`, // PUT/PATCH doctor
  DELETE_DOCTOR: (id: string) => `/user/doctors/${id}`, // DELETE doctor
};

// Other endpoints can be added here
