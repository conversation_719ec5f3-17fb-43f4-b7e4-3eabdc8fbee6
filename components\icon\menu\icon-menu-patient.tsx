import { FC } from "react";

interface IconMenuPatientsProps {
  className?: string;
}

const IconMenuPatients: FC<IconMenuPatientsProps> = ({ className }) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
     width="24"
      height="24"
      fill="none"
      viewBox="0 0 24 24"
      stroke="white"
    >
      <path
        stroke-linecap="round"
        stroke-linejoin="round"
        stroke-width="2"
        d="M12 12c2.21 0 4-1.79 4-4S14.21 4 12 4 8 5.79 8 8s1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"
      />
    </svg>
  );
};

export default IconMenuPatients;
