'use client';
import ContentAnimation from '@/src/components/layouts/content-animation';
import Footer from '@/src/components/layouts/footer';
import Header from '@/src/components/layouts/header';
import Loading from '@/src/components/layouts/loading';
import MainContainer from '@/src/components/layouts/main-container';
import Overlay from '@/src/components/layouts/overlay';
import ScrollToTop from '@/src/components/layouts/scroll-to-top';
import Sidebar from '@/src/components/layouts/sidebar';
import { useAuth } from '@/src/contexts/AuthContext';
import { useRouter } from 'next/navigation';
import { use, useEffect, useState } from 'react';

export default function DefaultLayout({ children }: { children: React.ReactNode }) {
    const { login, isAuthenticated, isLoading } = useAuth();
    const router = useRouter();
    const [checking, setchecking] = useState(true);

    // Redirect if already authenticated
    useEffect(() => {
        if (!isAuthenticated && !isLoading) {
            router.push('/login');
            return;
        }
        setchecking(false);
    }, [isAuthenticated, isLoading, router]);
    if (checking) return <Loading />;
    return (
        <>
            {/* BEGIN MAIN CONTAINER */}
            <div className="relative">
                <Overlay />
                <ScrollToTop />

                {/* BEGIN APP SETTING LAUNCHER */}

                {/* END APP SETTING LAUNCHER */}

                <MainContainer>
                    {/* BEGIN SIDEBAR */}
                    <Sidebar />
                    {/* END SIDEBAR */}
                    <div className="main-content flex min-h-screen flex-col">
                        {/* BEGIN TOP NAVBAR */}
                        <Header />
                        {/* END TOP NAVBAR */}

                        {/* BEGIN CONTENT AREA */}
                        <ContentAnimation>{children}</ContentAnimation>
                        {/* END CONTENT AREA */}

                        {/* BEGIN FOOTER */}
                        <Footer />
                        {/* END FOOTER */}
                    </div>
                </MainContainer>
            </div>
        </>
    );
}
