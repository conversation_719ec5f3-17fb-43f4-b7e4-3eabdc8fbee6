import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { API_BASE_URL } from '@/src/utils/apiRoutes';
import CryptoJs from 'crypto-js';

// Encryption key for token storage
const ENCRYPTION_KEY = "7d7cd92a9c3055f30f8943b5092abb8e";

// Helper functions for cookie management
function getCookie(name: string): string | null {
  if (typeof window === 'undefined') return null;
  
  const nameEQ = name + "=";
  const ca = document.cookie.split(';');
  for (let i = 0; i < ca.length; i++) {
    let c = ca[i];
    while (c.charAt(0) === ' ') c = c.substring(1, c.length);
    if (c.indexOf(nameEQ) === 0) return c.substring(nameEQ.length, c.length);
  }
  return null;
}

function deleteCookie(name: string) {
  if (typeof window === 'undefined') return;
  document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 UTC;path=/;`;
}

// Clear all authentication data
function clearAuthData() {
  deleteCookie('accessAdminToken');
  deleteCookie('user');
  if (typeof window !== 'undefined') {
    localStorage.removeItem('user');
  }
}

// Create the main API client
class APIClient {
  private client: AxiosInstance;

  constructor() {
    this.client = axios.create({
      baseURL: API_BASE_URL,
      timeout: 30000,
      withCredentials: true,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    this.setupInterceptors();
  }

  private setupInterceptors() {
    // Request interceptor to add auth token
    this.client.interceptors.request.use(
      (config) => {
        // Get token from cookies if available
        if (typeof window !== 'undefined') {
          const token = getCookie('accessAdminToken');
          if (token) {
            try {
              const decryptedToken = CryptoJs.AES.decrypt(token, ENCRYPTION_KEY);
              const tokenData = JSON.parse(decryptedToken.toString(CryptoJs.enc.Utf8));
              if (tokenData.accessToken) {
                config.headers.Authorization = `Bearer ${tokenData.accessToken}`;
              }
            } catch (error) {
              console.error('Error decrypting token:', error);
            }
          }
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Response interceptor to handle auth errors
    this.client.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error.response?.status === 401) {
          // Clear auth data and redirect to login
          clearAuthData();
          if (typeof window !== 'undefined') {
            window.location.href = '/login';
          }
        }
        return Promise.reject(error);
      }
    );
  }

  // Generic request methods
  async get<T = any>(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.client.get<T>(url, config);
  }

  async post<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.client.post<T>(url, data, config);
  }

  async put<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.client.put<T>(url, data, config);
  }

  async patch<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.client.patch<T>(url, data, config);
  }

  async delete<T = any>(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.client.delete<T>(url, config);
  }

  // Form data request (for login and other form-based endpoints)
  async postForm<T = any>(url: string, data: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    const formData = new URLSearchParams();
    
    // Convert object to form data
    Object.keys(data).forEach(key => {
      if (data[key] !== null && data[key] !== undefined) {
        formData.append(key, data[key]);
      }
    });

    return this.client.post<T>(url, formData, {
      ...config,
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        ...config?.headers,
      },
    });
  }

  // File upload method
  async uploadFile<T = any>(url: string, file: File, additionalData?: Record<string, any>): Promise<AxiosResponse<T>> {
    const formData = new FormData();
    formData.append('file', file);
    
    if (additionalData) {
      Object.keys(additionalData).forEach(key => {
        if (additionalData[key] !== null && additionalData[key] !== undefined) {
          formData.append(key, additionalData[key]);
        }
      });
    }

    return this.client.post<T>(url, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  }

  // Get the underlying axios instance if needed
  getClient(): AxiosInstance {
    return this.client;
  }
}

// Create and export a singleton instance
const apiClient = new APIClient();
export default apiClient;

// Export the class for testing or custom instances
export { APIClient };
