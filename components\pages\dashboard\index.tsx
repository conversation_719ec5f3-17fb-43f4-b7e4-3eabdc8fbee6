"use client";

import React, { useEffect, useState } from "react";
import Chart from "react-apexcharts";
import { ApexOptions } from "apexcharts";
import ApexCharts from "apexcharts";
import IconMenuDoctors from "@/components/icon/menu/icon-menu-doctor";
import IconMenuRoles from "@/components/icon/menu/icon-menu-roles";
import IconMenuEmployee from "@/components/icon/menu/icon-menu-employee";
import IconMenuPatients from "@/components/icon/menu/icon-menu-patient";
import { useMemo } from "react";
import Link from 'next/link';
import {
  EyeIcon,
  PencilSquareIcon,
  TrashIcon,
} from "@heroicons/react/24/outline";
import { useRouter } from "next/navigation";
import DataTable from "react-data-table-component";

type Patient = {
  id: number;
  first_name: string;
  last_name: string;
  email: string;
  username: string;
};

const initialPatient: Patient[] = [
  {
    id: 1,
    first_name: "<PERSON>",
    last_name: "<PERSON>",
    email: "<EMAIL>",
    username: "amartin",
  },
  {
    id: 2,
    first_name: "<PERSON>",
    last_name: "<PERSON>",
    email: "<EMAIL>",
    username: "rlee",
  },
  {
    id: 3,
    first_name: "<PERSON>",
    last_name: "Green",
    email: "<EMAIL>",
    username: "ngreen",
  },
  {
    id: 4,
    first_name: "David",
    last_name: "Clark",
    email: "<EMAIL>",
    username: "dclark",
  },
  {
    id: 5,
    first_name: "Chloe",
    last_name: "Harris",
    email: "<EMAIL>",
    username: "charris",
  },
  {
    id: 6,
    first_name: "Liam",
    last_name: "Walker",
    email: "<EMAIL>",
    username: "lwalker",
  },
  {
    id: 7,
    first_name: "Emma",
    last_name: "Robinson",
    email: "<EMAIL>",
    username: "erobinson",
  },
  {
    id: 8,
    first_name: "Daniel",
    last_name: "Lewis",
    email: "<EMAIL>",
    username: "dlewis",
  },
  {
    id: 9,
    first_name: "Grace",
    last_name: "Young",
    email: "<EMAIL>",
    username: "gyoung",
  },
  {
    id: 10,
    first_name: "Henry",
    last_name: "Scott",
    email: "<EMAIL>",
    username: "hscott",
  },
  {
    id: 10,
    first_name: "Henry",
    last_name: "Scott",
    email: "<EMAIL>",
    username: "hscott",
  },
];

let lastDate = new Date().getTime();
const XAXIS_RANGE = 60000; // 60 seconds

// Utility Functions
const generateDataPoint = () => {
  lastDate += 1000;
  return [lastDate, Math.floor(Math.random() * 80) + 10] as [number, number];
};

const getInitialData = (): [number, number][] => {
  const init: [number, number][] = [];
  for (let i = 0; i < 60; i++) {
    init.push(generateDataPoint());
  }
  return init;
};

const DashboardCharts = () => {
  const [areaSeries] = useState([
    {
      name: "Appointments",
      data: [12, 18, 20, 25, 22, 30, 28, 35, 40, 38, 42, 45],
    },
    {
      name: "Treatments",
      data: [5, 8, 10, 12, 15, 18, 20, 22, 25, 28, 30, 33],
    },
  ]);

  const areaOptions: ApexOptions = {
    chart: {
      height: 350,
      type: "area",
      dropShadow: { enabled: true, top: 3, left: 2, blur: 4, opacity: 0.15 },
    },
    colors: ["#EB6309", "#1E90FF"],
    dataLabels: { enabled: false },
    stroke: { curve: "smooth" },
    xaxis: {
      categories: [
        "Jan",
        "Feb",
        "Mar",
        "Apr",
        "May",
        "Jun",
        "Jul",
        "Aug",
        "Sep",
        "Oct",
        "Nov",
        "Dec",
      ],
    },
    tooltip: {
      x: { formatter: (val: string | number): string => String(val) },
    },
  };

  // Donut Chart
  const [donutSeries] = useState([35, 25, 50]);
  const donutOptions: ApexOptions = {
    chart: { type: "donut" },
    labels: ["Doctors", "Specialists", "Patients"],
    colors: ["#EB6309", "#1E90FF", "#22C55E"],
    legend: { position: "bottom" },
    dataLabels: { enabled: false },
    responsive: [
      {
        breakpoint: 480,
        options: {
          chart: { width: 280 },
          legend: { position: "bottom" },
        },
      },
    ],
  };

  const staticLineSeries = [
    {
      name: "Serious Cases",
      data: [35, 40, 38, 44, 42, 50, 55],
    },
    {
      name: "Normal Cases",
      data: [20, 25, 23, 27, 29, 32, 35],
    },
  ];

  const staticLineOptions: ApexOptions = {
    chart: {
      type: "line",
      height: 320,
    },
    stroke: {
      curve: "smooth",
      width: 2,
    },
    colors: ["#EF4444", "#3B82F6"],
    dataLabels: {
      enabled: false,
    },
    xaxis: {
      categories: ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"],
      title: {
        text: "Days",
      },
    },
    yaxis: {
      title: {
        text: "Case Count",
      },
    },

    tooltip: {
      y: {
        formatter: (val: number) => `${val} cases`,
      },
    },
  };

  const [timeFilter, setTimeFilter] = useState("Monthly");
  const [statusFilter, setStatusFilter] = useState("Delivered");

  const [patients, setPatients] = useState<Patient[]>(initialPatient);
  const [search, setSearch] = useState("");
  const router = useRouter();

  const handleDelete = (id: number) => {
    const confirmDelete = window.confirm(
      "Are you sure you want to delete this patient?"
    );
    if (confirmDelete) {
      setPatients((prev) => prev.filter((patient) => patient.id !== id));
    }
  };

  const handleEdit = (id: number) => {
    sessionStorage.setItem("editPatientId", id.toString());
    router.push("/patient/edit-patient");
  };

  const handleView = (id: number) => {
    sessionStorage.setItem("editPatientId", id.toString());
    router.push("/patient/view-patient");
  };

  const filteredPatients = patients.filter((patient) =>
    `${patient.first_name} ${patient.last_name} ${patient.email} ${patient.username}`
      .toLowerCase()
      .includes(search.toLowerCase())
  );

  const columns = [
    {
      name: "First Name",
      selector: (row: Patient) => row.first_name,
      sortable: true,
    },
    {
      name: "Last Name",
      selector: (row: Patient) => row.last_name,
      sortable: true,
    },
    { name: "Email", selector: (row: Patient) => row.email, sortable: true },
    {
      name: "Username",
      selector: (row: Patient) => row.username,
      sortable: true,
    },
    {
      name: "Actions",
      cell: (row: Patient) => (
        <div className="flex gap-3 items-center justify-center">
          <button onClick={() => handleView(row.id)} title="View">
            <EyeIcon className="h-5 w-5 text-blue-500 hover:text-blue-700" />
          </button>
          <button onClick={() => handleEdit(row.id)} title="Edit">
            <PencilSquareIcon className="h-5 w-5 text-green-500 hover:text-green-700" />
          </button>
          <button onClick={() => handleDelete(row.id)} title="Delete">
            <TrashIcon className="h-5 w-5 text-red-500 hover:text-red-700" />
          </button>
        </div>
      ),
    },
  ];

  return (
    <>
      <div className="p-6 bg-white rounded shadow-md mx-auto space-y-12">
        <h2 className="text-2xl font-bold mb-4">Dashboard</h2>
        {/* Top Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {/* Doctors Card */}
          <Link
            href="/doctor"
            className="bg-[#EB6309] text-white p-4 rounded shadow flex flex-col items-center gap-3 hover:opacity-90 transition"
          >
            <IconMenuDoctors className="text-4xl mb-2" />
            <p className="text-2xl font-extrabold">10</p>
            <span className="text-xl">Total Doctors</span>
          </Link>

          {/* Employees Card */}
          <Link
            href="/employee"
            className="bg-[#0D9488] text-white p-4 rounded shadow flex flex-col items-center gap-3 hover:opacity-90 transition"
          >
            <IconMenuEmployee className="text-4xl mb-2" />
            <p className="text-2xl font-extrabold">15</p>
            <span className="text-xl">Total Employees</span>
          </Link>

          {/* Patients Card */}
          <Link
            href="/patient"
            className="bg-[#6366F1] text-white p-4 rounded shadow flex flex-col items-center gap-3 hover:opacity-90 transition"
          >
            <IconMenuPatients className="text-4xl mb-2" />
            <p className="text-2xl font-extrabold">40</p>
            <span className="text-xl">Total Patients</span>
          </Link>
        </div>
        {/* Filters Row */}
        <div className="flex flex-col md:flex-row items-center gap-4">
          <select
            value={timeFilter}
            onChange={(e) => setTimeFilter(e.target.value)}
            className="border border-gray-300 px-4 py-2 rounded w-full bg-[transparent]"
          >
            <option>Daily</option>
            <option>Weekly</option>
            <option>Monthly</option>
            <option>Yearly</option>
          </select>

          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="border border-gray-300 px-4 py-2 rounded w-full bg-[transparent]"
          >
            <option>Delivered</option>
            <option>Pending</option>
            <option>Processing</option>
          </select>

          <button className="bg-[#EB6309] text-white px-6 py-2 rounded hover:opacity-90">
            Apply
          </button>
        </div>
        {/* Area Chart - Full Width */}
        {/* Donut & Line Chart - Side by Side */}
        <div className="flex flex-col md:flex-row md:space-x-6 space-y-8 md:space-y-0">
          {/* Donut Chart */}
          <div className="w-full md:w-1/2 bg-white p-4 rounded ">
            <h2 className="text-xl font-semibold mb-4 text-center text-gray-800">
              Doctors, Specialists & Patients
            </h2>
            <div className="flex justify-center">
              <Chart
                options={donutOptions}
                series={donutSeries}
                type="donut"
                height={320}
              />
            </div>
          </div>

          {/* Line Chart */}
          <div className="w-full md:w-1/2 bg-white p-4 rounded ">
            <h2 className="text-xl font-semibold mb-4 text-center text-gray-800">
              Live Serious/Normal Cases
            </h2>
            <Chart
              options={staticLineOptions}
              series={staticLineSeries}
              type="line"
              height={320}
            />
          </div>
        </div>
        <div>
          <h2 className="text-2xl font-bold text-center mb-4 text-gray-800">
            Monthly Appointments & Treatments
          </h2>
          <Chart
            options={areaOptions}
            series={areaSeries}
            type="area"
            height={350}
          />
        </div>
        <div>
          <h2 className="text-2xl font-bold text-center mb-4 text-gray-800">
            Patients Record
          </h2>
          <input
            type="text"
            placeholder="Search by name, email, or username..."
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            className="float-right w-full sm:w-1/3 mb-4 p-2 border border-gray-300 rounded"
          />
          <DataTable
            columns={columns}
            data={filteredPatients}
            pagination
            highlightOnHover
            responsive
            striped
            noHeader
          />
        </div>
      </div>
    </>
  );
};

export default DashboardCharts;
