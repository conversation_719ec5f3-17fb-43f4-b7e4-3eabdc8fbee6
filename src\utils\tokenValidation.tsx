import CryptoJs from 'crypto-js';

const _key = "7d7cd92a9c3055f30f8943b5092abb8e";

// Add a cache to prevent excessive validation of the same token
const tokenValidationCache = new Map<string, { isValid: boolean, timestamp: number }>();
const CACHE_TTL = 60 * 1000; // 1 minute cache TTL
const DEBUG_MODE = process.env.NODE_ENV === 'development';

export function validateToken(token: string): boolean {
  try {
    // Check cache first to avoid repeated validations of the same token
    const cachedResult = tokenValidationCache.get(token);
    if (cachedResult && (Date.now() - cachedResult.timestamp < CACHE_TTL)) {
      return cachedResult.isValid;
    }

    if (DEBUG_MODE) console.log('Starting token validation');

    // First decrypt the token
    const decryptedToken = CryptoJs.AES.decrypt(token, _key);
    const tokenString = decryptedToken?.toString(CryptoJs.enc.Utf8) ?? '';

    // Parse the token
    const tokenData = JSON.parse(tokenString);
    if (DEBUG_MODE) console.log('Parsed token data:', tokenData);

    // Check if token has required fields
    if (!tokenData || typeof tokenData !== 'object') {
      if (DEBUG_MODE) console.log('Invalid token structure');
      tokenValidationCache.set(token, { isValid: false, timestamp: Date.now() });
      return false;
    }

    // Check if token has the required JWT parts
    if (!tokenData.accessToken || typeof tokenData.accessToken !== 'string') {
      if (DEBUG_MODE) console.log('Token missing accessToken field');
      tokenValidationCache.set(token, { isValid: false, timestamp: Date.now() });
      return false;
    }

    // Additional validation for token format
    const tokenParts = tokenData.accessToken.split('.');
    if (tokenParts.length !== 3) {
      if (DEBUG_MODE) console.log('Invalid JWT token format');
      tokenValidationCache.set(token, { isValid: false, timestamp: Date.now() });
      return false;
    }

    // Try to decode the JWT parts to ensure they're valid base64
    try {
      const header = JSON.parse(atob(tokenParts[0]));
      const payload = JSON.parse(atob(tokenParts[1]));

      // Check if the token has required claims - updated to match server token structure
      if (!payload.id || !payload.iat || !payload.exp) {
        if (DEBUG_MODE) console.log('Token missing required claims');
        tokenValidationCache.set(token, { isValid: false, timestamp: Date.now() });
        return false;
      }

      // Check if token is expired
      const currentTime = Math.floor(Date.now() / 1000);
      if (payload.exp < currentTime) {
        if (DEBUG_MODE) console.log('Token is expired');
        tokenValidationCache.set(token, { isValid: false, timestamp: Date.now() });
        return false;
      }
    } catch (e) {
      if (DEBUG_MODE) console.log('Invalid JWT token encoding');
      tokenValidationCache.set(token, { isValid: false, timestamp: Date.now() });
      return false;
    }

    // Cache the successful validation result
    tokenValidationCache.set(token, { isValid: true, timestamp: Date.now() });
    return true;
  } catch (error) {
    if (DEBUG_MODE) console.error('Token validation error:', error);
    tokenValidationCache.set(token, { isValid: false, timestamp: Date.now() });
    return false;
  }
}

function isSessionExpired(token: string): boolean {
  try {
    const tokenData = JSON.parse(token);
    const currentTime = Math.floor(Date.now() / 1000);
    return tokenData.exp < currentTime;
  } catch (error) {
    return true; // If there's an error parsing the token, consider it expired
  }
}
