import axios, { AxiosResponse } from 'axios';
import { API_BASE_URL, AUTH_ENDPOINTS } from '@/src/utils/apiRoutes';
import CryptoJs from 'crypto-js';

// Types for authentication
export interface LoginRequest {
  email: string;
  password: string;
}

export interface LoginResponse {
  success: boolean;
  message: string;
  data?: {
    user: {
      id: string;
      email: string;
      name?: string;
      role?: string;
    };
    accessToken: string;
    refreshToken?: string;
  };
  error?: string;
}

export interface User {
  id: string;
  email: string;
  name?: string;
  role?: string;
}

// Encryption key for token storage
const ENCRYPTION_KEY = "7d7cd92a9c3055f30f8943b5092abb8e";

// Create axios instance with default config
const authAPI = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  withCredentials: true, // Important for cookie handling
  headers: {
    'Content-Type': 'application/x-www-form-urlencoded',
  },
});

// Request interceptor to add auth token to requests
authAPI.interceptors.request.use(
  (config) => {
    // Get token from cookies if available
    if (typeof window !== 'undefined') {
      const token = getCookie('accessAdminToken');
      if (token) {
        try {
          const decryptedToken = CryptoJs.AES.decrypt(token, ENCRYPTION_KEY);
          const tokenData = JSON.parse(decryptedToken.toString(CryptoJs.enc.Utf8));
          if (tokenData.accessToken) {
            config.headers.Authorization = `Bearer ${tokenData.accessToken}`;
          }
        } catch (error) {
          console.error('Error decrypting token:', error);
        }
      }
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle auth errors
authAPI.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Clear auth data and redirect to login
      clearAuthData();
      if (typeof window !== 'undefined') {
        window.location.href = '/login';
      }
    }
    return Promise.reject(error);
  }
);

// Helper functions for cookie management
function setCookie(name: string, value: string, days: number = 7) {
  if (typeof window === 'undefined') return;

  const expires = new Date();
  expires.setTime(expires.getTime() + (days * 24 * 60 * 60 * 1000));
  document.cookie = `${name}=${value};expires=${expires.toUTCString()};path=/;SameSite=Lax`;
}

function getCookie(name: string): string | null {
  if (typeof window === 'undefined') return null;

  const nameEQ = name + "=";
  const ca = document.cookie.split(';');
  for (let i = 0; i < ca.length; i++) {
    let c = ca[i];
    while (c.charAt(0) === ' ') c = c.substring(1, c.length);
    if (c.indexOf(nameEQ) === 0) return c.substring(nameEQ.length, c.length);
  }
  return null;
}

function deleteCookie(name: string) {
  if (typeof window === 'undefined') return;
  document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 UTC;path=/;`;
}

// Clear all authentication data
function clearAuthData() {
  deleteCookie('accessAdminToken');
  deleteCookie('user');
  if (typeof window !== 'undefined') {
    localStorage.removeItem('user');
  }
}

// Authentication API functions
export const authService = {
  // Login function
  async login(credentials: LoginRequest): Promise<LoginResponse> {
    try {
      // Create form data as required by the API
      const formData = new URLSearchParams();
      formData.append('email', credentials.email);
      formData.append('password', credentials.password);

      const response: AxiosResponse<LoginResponse> = await authAPI.post(
        AUTH_ENDPOINTS.LOGIN,
        formData,
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
        }
      );

      if (response.data.success && response.data.data) {
        // Encrypt and store the token
        const tokenData = {
          accessToken: response.data.data.accessToken,
          refreshToken: response.data.data.refreshToken,
          user: response.data.data.user,
        };

        const encryptedToken = CryptoJs.AES.encrypt(
          JSON.stringify(tokenData),
          ENCRYPTION_KEY
        ).toString();

        // Store encrypted token in cookie
        setCookie('accessAdminToken', encryptedToken, 7);

        // Store user data
        const encryptedUser = CryptoJs.AES.encrypt(
          JSON.stringify(response.data.data.user),
          ENCRYPTION_KEY
        ).toString();
        setCookie('user', encryptedUser, 7);

        // Also store in localStorage for easy access
        if (typeof window !== 'undefined') {
          localStorage.setItem('user', JSON.stringify(response.data.data.user));
        }
      }

      return response.data;
    } catch (error: any) {
      console.error('Login error:', error);

      // Handle different error scenarios
      if (error.response?.data) {
        return {
          success: false,
          message: error.response.data.message || 'Login failed',
          error: error.response.data.error || 'Authentication error',
        };
      }

      return {
        success: false,
        message: 'Network error. Please check your connection.',
        error: 'Network error',
      };
    }
  },

  // Logout function
  async logout(): Promise<void> {
    try {
      await authAPI.post(AUTH_ENDPOINTS.LOGOUT);
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      clearAuthData();
    }
  },

  // Get current user
  getCurrentUser(): User | null {
    if (typeof window === 'undefined') return null;

    try {
      const userCookie = getCookie('user');
      if (userCookie) {
        const decryptedUser = CryptoJs.AES.decrypt(userCookie, ENCRYPTION_KEY);
        return JSON.parse(decryptedUser.toString(CryptoJs.enc.Utf8));
      }

      // Fallback to localStorage
      const userStorage = localStorage.getItem('user');
      if (userStorage) {
        return JSON.parse(userStorage);
      }
    } catch (error) {
      console.error('Error getting current user:', error);
    }

    return null;
  },

  // Check if user is authenticated
  isAuthenticated(): boolean {
    if (typeof window === 'undefined') return false;

    const token = getCookie('accessAdminToken');
    return !!token;
  },

  // Get auth token
  getToken(): string | null {
    if (typeof window === 'undefined') return null;

    try {
      const token = getCookie('accessAdminToken');
      if (token) {
        const decryptedToken = CryptoJs.AES.decrypt(token, ENCRYPTION_KEY);
        const tokenData = JSON.parse(decryptedToken.toString(CryptoJs.enc.Utf8));
        return tokenData.accessToken;
      }
    } catch (error) {
      console.error('Error getting token:', error);
    }

    return null;
  },
};

export default authService;
