'use client';
import React, { useState } from 'react';
import <PERSON> from 'next/link';
import { EyeIcon, PencilSquareIcon, TrashIcon } from '@heroicons/react/24/outline';
import { useRouter } from 'next/navigation';

type Users = {
  id: number;
  first_name: string;
  last_name: string;
  name: string;
  role: string;
  Email: string;
};

const initialUsers: Users[] = [
  {
    id: 1,
    first_name: '<PERSON>', last_name: '<PERSON><PERSON>',
    name: 'joh<PERSON><PERSON>',
    role: 'Ad<PERSON>',
    Email: '<EMAIL>',
  },
  {
    id: 2,
    first_name: '<PERSON>', last_name: '<PERSON>',
    name: 'jane<PERSON>',
     role: 'Doctor',
    Email: '<EMAIL>',
  },
  {
    id: 3,
    first_name: '<PERSON>', last_name: '<PERSON>',
    name: 'm<PERSON><PERSON><PERSON>',
     role: 'Pat<PERSON>',
    Email: '<EMAIL>',
  },
  {
    id: 4,
    first_name: '<PERSON>', last_name: '<PERSON>',
    name: 'ed<PERSON><PERSON>',
     role: 'Employee',
    Email: '<EMAIL>',
  },
  {
    id: 5,
    first_name: '<PERSON>', last_name: '<PERSON>',
    name: 'wbrown',
     role: 'User',
    Email: 'will<PERSON>.<EMAIL>',
  },
];



const Users: React.FC = () => {
  const [users, setUsers] = useState<Users[]>(initialUsers);

  return (
    <div className="p-4 bg-white rounded shadow">
      <h2 className="text-2xl font-bold mb-4">Users List</h2>
      <table className="min-w-full border border-gray-200">
   <thead>
  <tr className="bg-gray-100">
    <th className="p-2 border">First Name</th>
    <th className="p-2 border">Last Name</th>
    <th className="p-2 border">Email</th>
    <th className="p-2 border">User Name</th>
    <th className="p-2 border">User Role</th>
    <th className="p-2 border text-center">Actions</th>
  </tr>
</thead>
<tbody>
  {users.map((user) => (
    <tr key={user.id} className="text-center hover:bg-gray-50">
      <td className="p-2 border">{user.first_name}</td>
      <td className="p-2 border">{user.last_name}</td>
      <td className="p-2 border">{user.Email}</td>
      <td className="p-2 border">{user.name}</td>
      <td className="p-2 border">{user.role}</td>
      <td className="p-2 border flex items-center justify-center gap-3">
        <button title="View">
          <EyeIcon className="h-5 w-5 text-blue-500 hover:text-blue-700" />
        </button>
        <button title="Edit">
          <PencilSquareIcon className="h-5 w-5 text-green-500 hover:text-green-700" />
        </button>
        <button title="Delete">
          <TrashIcon className="h-5 w-5 text-red-500 hover:text-red-700" />
        </button>
      </td>
    </tr>
  ))}
</tbody>

      </table>
    </div>
  );
};

export default Users;
