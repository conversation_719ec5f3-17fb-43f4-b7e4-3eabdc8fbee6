'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';

type Profile = {
  id: number;
  first_name: string;
  last_name: string;
  email: string;
  username: string;
  specialty: string;
};

// Static dummy profile data
const profileData: Profile[] = [
  {
    id: 1,
    first_name: '<PERSON>',
    last_name: '<PERSON><PERSON>',
    email: '<EMAIL>',
    username: 'johndo<PERSON>',
    specialty: 'Cardiology',
  },
];

const ViewProfilePage = () => {
  const router = useRouter();
  const [profile, setProfile] = useState<Profile | null>(null);

  useEffect(() => {
    // Use static ID = 1
    const id = 1;
    const foundProfile = profileData.find((d) => d.id === id);

    if (foundProfile) {
      setProfile(foundProfile);
    } else {
      router.push('/profile'); // fallback
    }
  }, []);

  if (!profile) return <div>Loading...</div>;

  return (
    <>      <h2 className="text-2xl font-bold mb-4">Profile Details</h2>
    <div className="bg-white p-4 rounded shadow-md mx-auto">
      <div className="grid grid-cols-2 gap-4">
        <div>
          <label className="font-medium">First Name:</label>
          <input value={profile.first_name} disabled className="border p-2 w-full bg-gray-100" />
        </div>
        <div>
          <label className="font-medium">Last Name:</label>
          <input value={profile.last_name} disabled className="border p-2 w-full bg-gray-100" />
        </div>
        <div>
          <label className="font-medium">Primary Account Email:</label>
          <input value={profile.email} disabled className="border p-2 w-full bg-gray-100" />
        </div>
        <div>
          <label className="font-medium">Username:</label>
          <input value={profile.username} disabled className="border p-2 w-full bg-gray-100" />
        </div>
      </div>
      <div className="flex gap-2 mt-4">
       <button
        //   onClick={handleSave}
          className="bg-[#EB6309] text-white px-4 py-2 rounded mt-4 "
        >
          Update Profile
        </button>
      <button
        onClick={() => router.push('/')}
        className="mt-4 bg-gray-500 text-white px-4 py-2 rounded"
      >
        Back
      </button>
    </div>
    </div>
    </>
  );
};

export default ViewProfilePage;
