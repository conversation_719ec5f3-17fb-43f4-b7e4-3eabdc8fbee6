'use client';

import React, { useState } from 'react';

type Doctor = {
  id: number;
  first_name: string;
  last_name: string;
  email: string;
  specialty: string;
  profile_image?: File | null;
};

type Props = {
  doctor: Doctor;
  setDoctor: React.Dispatch<React.SetStateAction<Doctor | null>>;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onSave: () => void;
  onCancel: () => void;
};

const EditDoctorForm: React.FC<Props> = ({ doctor, setDoctor, onChange, onSave, onCancel }) => {
  const [imagePreview, setImagePreview] = useState<string | null>(null);

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    setDoctor((prev) => prev ? { ...prev, profile_image: file } : prev);
    setImagePreview(URL.createObjectURL(file));
  };

  return (
    <div className="bg-white p-4 rounded shadow-md">
      <div className="grid grid-cols-2 gap-4">
        <div>
          <label className="font-bold">First Name:</label>
          <input value={doctor.first_name} disabled className="border p-2 w-full bg-gray-100" />
        </div>
        <div>
          <label className="font-bold">Last Name:</label>
          <input
            name="last_name"
            value={doctor.last_name}
            onChange={onChange}
            className="border p-2 w-full"
          />
        </div>
        <div>
          <label className="font-bold">Email:</label>
          <input
            name="email"
            value={doctor.email}
            onChange={onChange}
            className="border p-2 w-full"
          />
        </div>
        <div>
          <label className="font-bold">Specialty:</label>
          <input
            name="specialty"
            value={doctor.specialty}
            onChange={onChange}
            className="border p-2 w-full"
          />
        </div>
        <div>
          <label className="block mb-1 font-semibold">Profile Image</label>
          <input
            type="file"
            accept="image/*"
            onChange={handleImageChange}
            className="w-full border border-gray-300 px-4 py-2 rounded bg-white"
          />
          {imagePreview && (
            <img
              src={imagePreview}
              alt="Preview"
              className="mt-2 w-24 h-24 object-cover rounded border"
            />
          )}
        </div>
      </div>

      <div className="flex gap-2 mt-4">
        <button
          onClick={onSave}
          className="bg-[#EB6309] text-white px-4 py-2 rounded"
        >
          Save Changes
        </button>
        <button
          onClick={onCancel}
          className="bg-gray-500 text-white px-4 py-2 rounded"
        >
          Cancel
        </button>
      </div>
    </div>
  );
};

export default EditDoctorForm;
