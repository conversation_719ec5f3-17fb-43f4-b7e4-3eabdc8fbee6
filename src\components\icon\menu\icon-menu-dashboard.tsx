import { FC } from 'react';

interface IconMenuDashboardProps {
    className?: string;
}

const IconMenuDashboard: FC<IconMenuDashboardProps> = ({ className }) => {
    return (
       <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" viewBox="0 0 24 24">
  <rect x="3" y="3" width="7" height="7" rx="1" />
  <rect x="14" y="3" width="7" height="7" rx="1" />
  <rect x="14" y="14" width="7" height="7" rx="1" />
  <rect x="3" y="14" width="7" height="7" rx="1" />
</svg>

    );
};

export default IconMenuDashboard;
